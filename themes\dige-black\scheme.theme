// Most colors take from Monokai.
{
	"styles": [
		{
			"name": "fold.error",
			"style": {
				"foreground": "#FF0000"
			}
		},
		{
			"name": "fold.highlight",
			"style": {
				"foreground": "#66d9ef"
			}
		},
		{
			"name": "grep.mark",
			"style": {
				"background": "#60808080",
				"decorationStyle": "roundBox"
			}
		},
		{
			"name": "grep.match",
			"style": {
				"background": "#0065E0",
				"decorationStyle": "roundBox"
			}
		},
		{
			"name": "hex.column",
			"style": {
				"background": "transparent, #30808080"
			}
		},
		{
			"name": "hex.division",
			"style": {
				"foreground": "gray"
			}
		},
		{
			"name": "line.background",
			"style": {
				"background": "#1c1c1c, #f81c1c1c"
			}
		},
		{
			"name": "line.caret.command",
			"style": {
				"foreground": "orange"
			}
		},
		{
			"name": "line.caret.local",
			"style": {
				"foreground": "white"
			}
		},
		{
			"name": "line.caret.normal",
			"style": {
				"foreground": "white"
			}
		},
		{
			"name": "line.caret.remote",
			"style": {
				"foreground": "orange"
			}
		},
		{
			"name": "line.columnMarker",
			"style": {
				"background": "gray"
			}
		},
		{
			"name": "line.highlight",
			"style": {
				"foreground": "#FF008F",
				"background": "#207F7FD0"
			}
		},
		{
			"name": "line.indentGuide",
			"style": {
				"foreground": "gray"
			}
		},
		{
			"name": "line.modified",
			"style": {
				"background": "#FACA3A"
			}
		},
		{
			"name": "line.saved",
			"style": {
				"background": "#00FF00"
			}
		},
		{
			"name": "line.unsaved",
			"style": {
				"background": "#FFF020"
			}
		},
		{
			"name": "line.wrapSymbol",
			"style": {
				"foreground": "#00A0C0"
			}
		},
		{
			"name": "margin.blank",
			"style": {
				"foreground": "#FF0000"
			}
		},
		{
			"name": "margin.fold",
			"style": {
				"foreground": "#909090"
			}
		},
		{
			"name": "margin.number",
			"style": {
				"foreground": "#2B91AF"
			}
		},
		{
			"name": "margin.symbol",
			"style": {
				"background": "transparent"
			}
		},
		{
			"name": "margin.text",
			"style": {
				"background": "transparent"
			}
		},
		{
			"name": "margin.timestamp",
			"style": {
				"foreground": "gray"
			}
		},
		{
			"name": "pair.highlightMatched",
			"style": {
				"decorationStyle": "roundBox",
				"decorationForeground": "#8000FFFF",
				"decorationBackground": "#4000FFFF"
			}
		},
		{
			"name": "pair.highlightUnmatched",
			"style": {
				"decorationStyle": "roundBox",
				"decorationForeground": "#80FF0000",
				"decorationBackground": "#40FF0000"
			}
		},
		{
			"name": "pair.level1",
			"style": {
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "pair.level2",
			"style": {
				"foreground": "orchid"
			}
		},
		{
			"name": "pair.level3",
			"style": {
				"foreground": "dodgerblue"
			}
		},
		{
			"name": "pair.level4",
			"style": {
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "pair.level5",
			"style": {
				"foreground": "orchid"
			}
		},
		{
			"name": "pair.level6",
			"style": {
				"foreground": "dodgerblue"
			}
		},
		{
			"name": "pair.levelUnmatched",
			"style": {
				"foreground": "red"
			}
		},
		{
			"name": "terminal.ansiBlack",
			"style": {
				"foreground": "#333333"
			}
		},
		{
			"name": "terminal.ansiRed",
			"style": {
				"foreground": "#C4265E"
			}
		},
		{
			"name": "terminal.ansiGreen",
			"style": {
				"foreground": "#86B42B"
			}
		},
		{
			"name": "terminal.ansiYellow",
			"style": {
				"foreground": "#D0A500"
			}
		},
		{
			"name": "terminal.ansiBlue",
			"style": {
				"foreground": "#3465A4"
			}
		},
		{
			"name": "terminal.ansiMagenta",
			"style": {
				"foreground": "#8C6BC8"
			}
		},
		{
			"name": "terminal.ansiCyan",
			"style": {
				"foreground": "#56ADBC"
			}
		},
		{
			"name": "terminal.ansiWhite",
			"style": {
				"foreground": "#e3e3dd"
			}
		},
		{
			"name": "terminal.ansiBrightBlack",
			"style": {
				"foreground": "#666666"
			}
		},
		{
			"name": "terminal.ansiBrightRed",
			"style": {
				"foreground": "#f92672"
			}
		},
		{
			"name": "terminal.ansiBrightGreen",
			"style": {
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "terminal.ansiBrightYellow",
			"style": {
				"foreground": "#9e862f"
			}
		},
		{
			"name": "terminal.ansiBrightBlue",
			"style": {
				"foreground": "#819aff"
			}
		},
		{
			"name": "terminal.ansiBrightMagenta",
			"style": {
				"foreground": "#AE81FF"
			}
		},
		{
			"name": "terminal.ansiBrightCyan",
			"style": {
				"foreground": "#66D9EF"
			}
		},
		{
			"name": "terminal.ansiBrightWhite",
			"style": {
				"foreground": "#f8f8f2"
			}
		},
		{
			"name": "terminal.prompt",
			"style": {
				"foreground": "#909090",
				"decorationStyle": "lineBackground",
				"decorationBackground": "#30C4E9F2"
			}
		},
		{
			"name": "text.controlChar",
			"style": {
				"foreground": "#007F00"
			}
		},
		{
			"name": "text.default",
			"style": {
				"foreground": "#F8F8F2",
				"background": "#1c1c1c",
				"fontFamilies": "RobotoMono Nerd Font, Fira Code, Lucida Sans Typewriter, Lucida Console, Monoca",
				"fontSize": 10
			}
		},
		{
			"name": "text.folded",
			"style": {
				"foreground": "#808080",
				"background": "#30808080",
				"fontStyle": "italic",
				"decorationStyle": "roundBox"
			}
		},
		{
			"name": "text.hotSpot",
			"style": {
				"foreground": "#007acc",
				"decorationStyle": "solidLine",
				"decorationForeground": "#007acc"
			}
		},
		{
			"name": "text.inserted",
			"style": {
				"decorationStyle": "roundBox",
				"decorationForeground": "#8000FF00",
				"decorationBackground": "#8000FF00"
			}
		},
		{
			"name": "text.link",
			"style": {
				"foreground": "#66D9EF",
				"decorationStyle": "solidLine",
				"decorationForeground": "gray"
			}
		},
		{
			"name": "text.removed",
			"style": {
				"decorationStyle": "roundBox",
				"decorationForeground": "#80FF0000",
				"decorationBackground": "#80FF0000"
			}
		},
		{
			"name": "text.selection",
			"style": {
				"foreground": "#805050FF",
				"background": "#805050FF",
				"decorationStyle": "roundBox"
			}
		},
		{
			"name": "text.syncEdit",
			"style": {
				"foreground": "#78FFFF00",
				"decorationStyle": "box",
				"decorationForegroundground": "#780000FF"
			}
		},
		{
			"name": "text.visitedLink",
			"style": {
				"foreground": "purple",
				"decorationStyle": "solidLine",
				"decorationForeground": "purple"
			}
		},
		{
			"name": "text.whiteSpace",
			"style": {
				"foreground": "gray"
			}
		}
	],
	"scopes": [
		{
			"scope": "number.context.grep",
			"style": {
				"foreground": "gray"
			}
		},
		{
			"scope": "file.grep",
			"style": {
				"foreground": "#66FFFF"
			}
		},
		{
			"scope": "number.line.grep",
			"style": {
				"foreground": "red"
			}
		},
		{
			"scope": "path.grep",
			"style": {
				"foreground": "gray",
				"decorationStyle": "lineBackground",
				"decorationBackground": "#207F7FD0"
			}
		},
		{
			"scope": [
				"meta.embedded",
				"source.groovy.embedded"
			],
			"style": {
				"background": "#1c1c1c",
				"foreground": "#F8F8F2"
			}
		},
		{
			"name": "Comment",
			"scope": "comment, disabled",
			"style": {
				"foreground": "#75715E"
			}
		},
		{
			"name": "String",
			"scope": "string",
			"style": {
				"foreground": "#E6DB74"
			}
		},
		{
			"name": "Punctuation",
			"scope": [
				"punctuation.definition.delimiter",
				"punctuation.definition.block"
			],
			"style": {
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "Template Definition",
			"scope": [
				"punctuation.definition.template-expression",
				"punctuation.section.embedded"
			],
			"style": {
				"foreground": "#F92672"
			}
		},
		{
			"name": "Reset JavaScript string interpolation expression",
			"scope": [
				"meta.template.expression"
			],
			"style": {
				"foreground": "#F8F8F2"
			}
		},
		{
			"name": "Number",
			"scope": "constant.numeric",
			"style": {
				"foreground": "#AE81FF"
			}
		},
		{
			"name": "Built-in constant",
			"scope": "constant.language",
			"style": {
				"foreground": "#AE81FF"
			}
		},
		{
			"name": "User-defined constant",
			"scope": "constant.character, constant.other",
			"style": {
				"foreground": "#AE81FF"
			}
		},
		{
			"name": "Variable",
			"scope": "variable",
			"style": {
				"fontStyle": "",
				"foreground": "#F8F8F2"
			}
		},
		{
			"name": "Keyword",
			"scope": "keyword",
			"style": {
				"foreground": "#F92672"
			}
		},
		{
			"name": "Sign",
			"scope": "sign.directive.prompt",
			"style": {
				"foreground": "#F92672"
			}
		},
		{
			"name": "Storage",
			"scope": "storage",
			"style": {
				"fontStyle": "",
				"foreground": "#F92672"
			}
		},
		{
			"name": "Storage type",
			"scope": "storage.type",
			"style": {
				"fontStyle": "italic",
				"foreground": "#66D9EF"
			}
		},
		{
			"name": "Class name",
			"scope": "entity.name.type, entity.name.class",
			"style": {
				"foreground": "#A6E22E",
				"fontStyle": "italic underline"
			}
		},
		{
			"name": "Inherited class",
			"scope": "entity.other.inherited-class",
			"style": {
				"foreground": "#A6E22E",
				"fontStyle": "italic",
				"decorationStyle": "solidLine"
			}
		},
		{
			"name": "Function name",
			"scope": "entity.name.function",
			"style": {
				"fontStyle": "",
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "Function argument",
			"scope": "variable.parameter",
			"style": {
				"fontStyle": "italic",
				"foreground": "#FD971F"
			}
		},
		{
			"name": "Tag name",
			"scope": "entity.name.tag",
			"style": {
				"fontStyle": "",
				"foreground": "#F92672"
			}
		},
		{
			"name": "Tag attribute",
			"scope": "entity.other.attribute-name",
			"style": {
				"fontStyle": "",
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "Library function",
			"scope": "support.function",
			"style": {
				"fontStyle": "",
				"foreground": "#66D9EF"
			}
		},
		{
			"name": "Library constant",
			"scope": "support.constant",
			"style": {
				"fontStyle": "",
				"foreground": "#66D9EF"
			}
		},
		{
			"name": "Library class/type",
			"scope": "support.type, support.class",
			"style": {
				"fontStyle": "italic",
				"foreground": "#66D9EF"
			}
		},
		{
			"name": "Library variable",
			"scope": "support.other.variable",
			"style": {
				"fontStyle": ""
			}
		},
		{
			"name": "Invalid",
			"scope": "invalid",
			"style": {
				"background": "#F92672",
				"fontStyle": "",
				"foreground": "#F8F8F0",
				"decorationStyle": "lineEndingBackground",
				"decorationBackground": "#F92672"
			}
		},
		{
			"name": "Invalid deprecated",
			"scope": "invalid.deprecated, invalid.illegal",
			"style": {
				"foreground": "#F92672"
			}
		},
		{
			"name": "JSON String",
			"scope": "meta.structure.dictionary.json string.quoted.double.json",
			"style": {
				"foreground": "#CFCFC2"
			}
		},
		{
			"name": "diff.header",
			"scope": "meta.diff, meta.diff.header",
			"style": {
				"foreground": "#75715E"
			}
		},
		{
			"name": "diff.deleted",
			"scope": "markup.deleted",
			"style": {
				"foreground": "#F92672"
			}
		},
		{
			"name": "diff.inserted",
			"scope": "markup.inserted",
			"style": {
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "diff.changed",
			"scope": "markup.changed",
			"style": {
				"foreground": "#E6DB74"
			}
		},
		{
			"scope": "constant.numeric.line-number.find-in-files - match",
			"style": {
				"foreground": "#AE81FFA0"
			}
		},
		{
			"scope": "entity.name.filename.find-in-files",
			"style": {
				"foreground": "#E6DB74"
			}
		},
		{
			"name": "Markup Quote",
			"scope": "markup.quote",
			"style": {
				"foreground": "#F92672"
			}
		},
		{
			"name": "Markup Lists",
			"scope": "markup.list",
			"style": {
				"foreground": "#E6DB74"
			}
		},
		{
			"name": "Markup Styling",
			"scope": "markup.bold, markup.italic",
			"style": {
				"foreground": "#66D9EF"
			}
		},
		{
			"name": "Markup Inline",
			"scope": "markup.inline.raw",
			"style": {
				"fontStyle": "",
				"foreground": "#FD971F"
			}
		},
		{
			"name": "Markup Headings",
			"scope": "markup.heading",
			"style": {
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "Markup Setext Header",
			"scope": "markup.heading.setext",
			"style": {
				"fontStyle": "",
				"foreground": "#A6E22E"
			}
		},
		{
			"name": "Prompt Line",
			"scope": "prompt.line",
			"style": {
				"decorationStyle": "lineBackground",
				"decorationBackground": "#262626"
			}
		},
		{
			"scope": "token.success-token",
			"style": {
				"foreground": "limegreen"
			}
		},
		{
			"scope": "token.info-token",
			"style": {
				"foreground": "#6796e6"
			}
		},
		{
			"scope": "token.warn-token",
			"style": {
				"foreground": "#cd9731"
			}
		},
		{
			"scope": "token.error-token",
			"style": {
				"foreground": "#f44747"
			}
		},
		{
			"scope": "token.debug-token",
			"style": {
				"foreground": "#b267e6"
			}
		},
		{
			"name": "this.self",
			"scope": "variable.language",
			"style": {
				"foreground": "#FD971F"
			}
		},
		{
			"name": "identifier",
			"scope": "identifier.text, meta.function-call",
			"style": {
				"foreground": "coral, mediumorchid, palevioletred"
			}
		}
	]
}