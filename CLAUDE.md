# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 仓库概述

这是WindTerm终端模拟器的全局配置目录，包含主题、本地化文件、配置设置和自定义数据。

## 架构说明

### 配置系统
- **wind.config**: 核心应用设置（JSON格式），涵盖UI主题、编辑器行为、终端设置和文本显示首选项
- **wind.variables**: 基于JavaScript的变量定义，用于动态内容如文件路径、日期和文本选择
- **wind.actions**: 大量预定义操作和命令集合（44K+标记）
- **wind.keymaps**: 全面的键盘映射定义（26K+标记）

### 目录结构
- **locales/**: 33+种语言的国际化文件，包含翻译映射
- **themes/**: UI和颜色方案定义
  - `default/`: 包含字体、音频文件、图标和基础主题资源
  - `dige-*`: 自定义主题变体（黑色、白色、黑白）
- **palettes/**: 特定功能的调色板定义

### 关键配置区域

#### 应用程序设置 (wind.config)
- 语言和主题选择：`application.language`、`application.themeGui`
- 代理和网络配置：`application.proxy`、`application.proxyDns`
- 搜索引擎定义：Google、Bing、Github、Stackoverflow等
- 窗口透明度和UI行为：`application.windowOpacity`

#### 文本编辑器配置
- 字体设置：默认Roboto Mono，10pt字体大小
- 行号、边距和显示选项：`text.margins`配置
- 缩进（默认4个空格）、换行和格式化：`text.indentSize`、`text.tabSize`
- 语法高亮和代码折叠：`text.highlightPair`、`text.highlightFold`

#### 终端配置
- 自动补全和命令历史：30天保留，10K命令容量
- 鼠标跟踪和交互设置：`terminal.mouseTracking.*`
- 认证和登录向导默认值：`terminal.loginWizard.defaultAuthType`
- 屏幕管理和粘贴对话框行为

### 变量系统 (wind.variables)
应用程序中可访问的动态变量：
- 文件操作：`FilePath`、`FileName`、`FileDir`、`FileExt`
- 文本上下文：`Selection`、`CurrentWordText`、`CurrentLineText`
- 编辑器状态：`CaretLine`、`CaretColumn`、`BufferText`
- 系统信息：`Date`、`Time`、`DateTime`、`AppDir`

## 配置修改指南

### 修改设置
- 编辑`wind.config`进行应用程序范围的设置
- 修改`themes/`目录中的主题文件进行UI自定义
- 更新locale文件进行翻译更改

### 添加主题
- 在`themes/`下创建新主题目录
- 包含`gui.theme`、`scheme.theme`和`icon.theme`文件
- 如需要，在`images/`子目录中添加自定义资源

### 本地化
- 语言环境文件使用JSON格式，包含`translations`数组
- 每个条目将英文文本（`en`）映射到翻译文本（`tr`）
- 包含贡献者信息和语言元数据

### 重要注意事项
- 大型配置文件（actions和keymaps）包含复杂的操作定义
- 所有配置都采用JSON格式，修改时需要保持有效的JSON结构
- 主题系统支持完全自定义的外观和感觉